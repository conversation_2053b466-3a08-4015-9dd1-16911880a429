# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

永远使用中文回复

## 项目概述

这是一个基于 PyQt5 的 Android 设备控制系统，使用赛博朋克风格的 UI 界面。项目通过 PyAibote 库实现 Android 设备的远程控制和管理。

## 核心架构

### 主要模块结构

- `main.py` - 程序入口点，包含 Android 控制线程和设备脚本类
- `UI/` - UI 组件包结构
  - `main_window.py` - 主窗口类（CyberpunkMainWindow）
  - `样式/赛博朋克主题.py` - 统一的 UI 样式管理
  - `标题栏/` - 自定义标题栏组件
  - `导航面板/` - 左侧导航组件
  - `服务控制/` - 服务启动/停止控制组件
  - `输出窗口/` - 日志输出显示组件
  - `连接管理/` - 设备连接表格管理
  - `账号管理/` - 账号管理功能（已禁用）
  - `系统设置/` - 系统配置页面

### 关键设计模式

1. **组件化架构**: UI 采用组件化设计，每个功能模块独立封装
2. **线程分离**: 使用 AndroidControlThread (QThread) 处理设备控制，避免 UI 阻塞
3. **信号槽机制**: 大量使用 PyQt5 的信号槽实现组件间通信
4. **主题统一管理**: CyberpunkTheme 类集中管理所有 UI 样式

## 开发命令

### 运行应用程序
```bash
python main.py
```

### 测试 UI 组件导入
```bash
python -c "from UI import CyberpunkMainWindow; print('导入成功')"
```

### 检查依赖库
```bash
python -c "import PyQt5; from PyQt5.QtCore import PYQT_VERSION_STR; print('PyQt5版本:', PYQT_VERSION_STR)"
python -c "import PyAibote; print('PyAibote导入成功')"
```

## 核心依赖

- **Python 3.11.9**
- **PyQt5 5.15.10** - GUI 框架
- **PyAibote** - Android 设备控制库（核心依赖）
- **winreg** - Windows 注册表操作（用于配置存储）

## 重要开发注意事项

### 设备连接机制

设备支持两种操作模式：
1. **无障碍模式** - 通过 Android 无障碍服务控制
2. **HID 模式** - 通过 Windows HID 驱动控制

设备连接信息存储在 Windows 注册表：`HKEY_CURRENT_USER\Software\Aibote\ServerConfig`

### UI 组件开发规范

- 所有 UI 组件继承自对应的 PyQt5 基类
- 使用 `CyberpunkTheme` 类的预定义样式
- 组件间通信优先使用信号槽机制
- 新组件需在对应的 `__init__.py` 中导出

### 线程安全

- UI 更新必须在主线程进行
- 设备操作通过 AndroidControlThread 在后台执行
- 使用信号槽在线程间传递数据

### 样式约定

- 主色调：赛博朋克蓝 (#00F5FF)
- 背景：半透明深色 (rgba(20, 25, 45, 0.7))
- 字体：Segoe UI 系列，代码使用 Consolas
- 所有按钮和面板使用圆角边框和发光效果

## 故障排除

### 常见问题

1. **设备连接失败** - 检查无障碍服务是否开启或 HID 驱动是否正常
2. **UI 组件导入错误** - 确保所有 `__init__.py` 文件正确配置
3. **线程通信问题** - 检查信号槽连接是否正确建立

### 调试命令

```bash
# 测试窗口创建
python -c "
import sys
from UI import CyberpunkMainWindow
from PyQt5.QtWidgets import QApplication
app = QApplication(sys.argv)
window = CyberpunkMainWindow()
print('窗口创建成功!')
app.quit()
"
```