from datetime import datetime
from PyQt5.QtWidgets import QFrame, QVBoxLayout, QTextEdit, QPushButton
from ..样式 import CyberpunkTheme


class OutputWindowComponent(QFrame):
    """输出窗口组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        """初始化输出窗口UI"""
        self.setStyleSheet(CyberpunkTheme.OUTPUT_FRAME_STYLE)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 输出文本框
        self.output_text = QTextEdit()
        self.output_text.setReadOnly(True)
        self.output_text.setStyleSheet(CyberpunkTheme.OUTPUT_TEXT_STYLE)
        
        # 创建浮空清空日志按钮
        self.clear_output_btn = QPushButton("清空日志", self.output_text)
        self.clear_output_btn.setFixedWidth(80)
        self.clear_output_btn.setStyleSheet(CyberpunkTheme.CLEAR_BUTTON_STYLE)
        self.clear_output_btn.clicked.connect(self.output_text.clear)
        
        # 重写resizeEvent来更新按钮位置
        def resize_event(event):
            self.clear_output_btn.move(self.output_text.width() - 110, self.output_text.height() - 30)
            QTextEdit.resizeEvent(self.output_text, event)
        self.output_text.resizeEvent = resize_event
        
        # 初始定位按钮 (向左移动20像素避免遮挡滚动条)
        self.clear_output_btn.move(self.output_text.width() - 110, self.output_text.height() - 30)
        self.clear_output_btn.raise_()  # 确保按钮在最上层
        
        layout.addWidget(self.output_text)
    
    def append_output(self, typ, msg):
        """向输出窗口添加消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 根据类型设置颜色
        color = "#C0C0C0"  # 默认灰色
        if typ == "错误":
            color = "#FF5555"
        elif typ == "警告":
            color = "#FFA500"
        elif typ == "系统":
            color = "#00F5FF"
        elif typ == "信息":
            color = "#00FF00"
        
        # 使用HTML格式添加彩色文本
        self.output_text.append(f'<font color="{color}">[{timestamp}] [{typ}] {msg}</font>')
        # 自动滚动到最新内容
        scroll_bar = self.output_text.verticalScrollBar()
        scroll_bar.setValue(scroll_bar.maximum())
    
    def clear_output(self):
        """清空输出窗口"""
        self.output_text.clear()