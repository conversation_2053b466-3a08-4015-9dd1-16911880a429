from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QWidget, QHBoxLayout, QLabel
from ..样式 import CyberpunkTheme


class TitleBarComponent(QWidget):
    """标题栏组件"""
    
    def __init__(self, main_window, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self.setFixedHeight(38)
        self.setStyleSheet("background-color: transparent;")
        
        # 初始化拖拽相关变量
        self._is_dragging = False
        self._drag_start_position = None
        
        self.init_ui()
    
    def init_ui(self):
        """初始化标题栏UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 0, 10, 0)
        
        # 左侧标题
        self._create_title_label(layout)
    
    def _create_title_label(self, layout):
        """创建标题标签"""
        title_label = QLabel("Aibote服务端控制中心")
        title_label.setStyleSheet(CyberpunkTheme.TITLE_LABEL_STYLE)
        layout.addWidget(title_label)
    
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self._is_dragging = True
            self._drag_start_position = event.globalPos() - self.main_window.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self._is_dragging and event.buttons() == Qt.LeftButton:
            self.main_window.move(event.globalPos() - self._drag_start_position)
            event.accept()
            
            # 隐藏账号管理浮窗
            if hasattr(self.main_window, 'account_page') and hasattr(self.main_window.account_page, 'current_server_ui'):
                if self.main_window.account_page.current_server_ui and self.main_window.account_page.current_server_ui.isVisible():
                    self.main_window.account_page.current_server_ui.setVisible(False)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self._is_dragging = False
            event.accept()