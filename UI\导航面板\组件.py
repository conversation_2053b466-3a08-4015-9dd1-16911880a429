from PyQt5.QtWidgets import QFrame, QVBoxLayout, QPushButton
from ..样式 import CyberpunkTheme


class NavigationComponent(QFrame):
    """导航面板组件"""
    
    def __init__(self, main_window, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self.nav_buttons = []
        self.init_ui()
    
    def init_ui(self):
        """初始化导航面板UI"""
        self.setFixedWidth(130)
        self.setStyleSheet(CyberpunkTheme.NAV_FRAME_STYLE)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 添加导航按钮
        nav_buttons = [
            ("连接管理", "📶"),
            ("账号管理", "👤"),
            ("系统设置", "⚙️")
        ]
        
        for text, icon in nav_buttons:
            btn = self._create_nav_button(text, icon)
            self.nav_buttons.append(btn)
            layout.addWidget(btn)
        
        # 添加Stretch填充剩余空间
        layout.addStretch()
    
    def _create_nav_button(self, text, icon):
        """创建导航按钮"""
        btn = QPushButton(f"  {icon}   {text}")
        btn.setFixedHeight(45)
        btn.setCheckable(True)
        btn.setStyleSheet(CyberpunkTheme.NAV_BUTTON_STYLE)
        
        # 绑定按钮点击事件
        btn.clicked.connect(lambda: self.switch_page(text, btn))
        
        return btn
    
    def switch_page(self, page_name, button=None):
        """切换内容页面"""
        try:
            print(f"切换到页面: {page_name}")
            
            # 更新按钮选中状态
            if button and self.nav_buttons:
                # 取消所有按钮的选中状态
                for btn in self.nav_buttons:
                    btn.setChecked(False)
                # 设置当前按钮为选中状态
                button.setChecked(True)
            
            # 在切换前清除当前页面的选中状态
            if hasattr(self.main_window, 'stacked_widget'):
                current_widget = self.main_window.stacked_widget.currentWidget()
                if hasattr(current_widget, 'clear_selection'):
                    current_widget.clear_selection()
            
            # 隐藏账号管理浮窗
            if hasattr(self.main_window, 'account_page') and hasattr(self.main_window.account_page, 'current_server_ui'):
                if self.main_window.account_page.current_server_ui and self.main_window.account_page.current_server_ui.isVisible():
                    self.main_window.account_page.current_server_ui.setVisible(False)
            
            # 根据页面名称切换堆叠窗口的当前页
            if hasattr(self.main_window, 'stacked_widget'):
                if page_name == "连接管理":
                    self.main_window.stacked_widget.setCurrentIndex(0)
                elif page_name == "账号管理":
                    self.main_window.stacked_widget.setCurrentIndex(1)
                elif page_name == "系统设置":
                    self.main_window.stacked_widget.setCurrentIndex(2)
            
            if hasattr(self.main_window, 'append_output'):
                self.main_window.append_output("系统", f"切换到 {page_name} 页面")
                
        except Exception as e:
            if hasattr(self.main_window, 'append_output'):
                self.main_window.append_output("错误", f"切换页面时发生错误: {str(e)}")