import sys
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)
from PyQt5.QtCore import qInstallMessageHandler, QtMsgType, Qt, QThread, pyqtSignal, QTimer
import winreg
from datetime import datetime
from PyQt5.QtWidgets import QApplication
from PyAibote import AndroidBotMain, WinBotMain
import time, traceback

def qt_message_handler(mode, context, message):
    """过滤QTextCursor和QVector<int>相关的警告"""
    if "QTextCursor" not in message and "QVector<int>" not in message:
        print(message)

# 安装Qt消息处理器
qInstallMessageHandler(qt_message_handler)

# 必须在创建QApplication前设置高DPI
QApplication.setAttribute(Qt.AA_EnableHighDpiScaling)


# PyQt5 后台线程类,继承自QThread，负责在后台运行Android服务        
class AndroidControlThread(QThread):
    """Android设备控制线程"""
    progress = pyqtSignal(str)  # 简化为直接发送消息文本
    device_info_updated = pyqtSignal(dict)  # 设备信息更新信号
    device_removed_by_ip = pyqtSignal(str)  # 通过IP删除设备信号

    def __init__(self):
        super().__init__()
        self._load_config()
        self.ip = "127.0.0.1"
        self.port = 8888
        self.stop_flag = False
        
        # 创建定时器用于设备操作，减少频率避免阻塞UI
        self.device_check_timer = QTimer()
        self.device_check_timer.timeout.connect(self._check_device_status)
        self.device_check_timer.setInterval(5000)  # 5秒检查一次，减少频率
        
        # 表格更新防抖定时器
        self.table_update_timer = QTimer()
        self.table_update_timer.setSingleShot(True)
        self.table_update_timer.timeout.connect(self._perform_table_update)
        
        # 活跃设备列表
        self.active_devices = {}

    def _load_config(self):
        """从注册表加载配置"""
        try:
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Aibote\ServerConfig")
            self.ip = winreg.QueryValueEx(key, "ServerIP")[0]
            self.port = int(winreg.QueryValueEx(key, "ServerPort")[0])
            winreg.CloseKey(key)
        except:
            self.ip, self.port = "127.0.0.1", 8888  # 默认值
            
    def run(self):
        """线程主运行方法"""
        try:
            self.progress.emit(f'服务启动成功 IP:{self.ip} 端口:{self.port}')
            # 启动设备检查定时器（低频率）
            self.device_check_timer.start()
            # 设置线程引用到脚本类
            CustomAndroidScript.set_thread_reference(self)
            # 在新线程中执行服务启动
            from threading import Thread
            Thread(target=CustomAndroidScript.execute, args=(self.ip, self.port), daemon=True).start()
        except Exception as e:
            self.progress.emit(f'服务启动失败: {str(e)}')

    def stop_service(self):
        """停止服务"""
        try:
            CustomAndroidScript.StopSrver()
            self.device_check_timer.stop()
            self.table_update_timer.stop()
            self.stop_flag = True
            self.progress.emit('服务关闭成功')
        except Exception as e:
            self.progress.emit(f'服务关闭失败: {str(e)}')

    def _perform_table_update(self):
        """执行表格更新(防抖机制)"""
        # 批量更新设备状态，减少UI刷新频率
        if self.active_devices:
            for device_id, device_info in self.active_devices.items():
                self.device_info_updated.emit(device_info)
    
    def _check_device_status(self):
        """定期检查设备状态（低频率，避免阻塞UI）"""
        if not self.stop_flag:
            # 发送轻量级的状态检查信号
            self.progress.emit("检查设备状态中...")
            # 更新活跃设备状态
            if self.active_devices:
                self.table_update_timer.start(500)  # 延迟500ms更新UI

    def add_active_device(self, device_info):
        """添加活跃设备到缓存"""
        device_id = device_info.get('device_id', '')
        if device_id:
            self.active_devices[device_id] = device_info
            
    def remove_active_device(self, device_id):
        """从缓存中移除设备"""
        if device_id in self.active_devices:
            del self.active_devices[device_id]
            
    def set_server_info(self, ip, port):
        """设置服务器信息"""
        self.ip, self.port = ip, port


# 2. 自定义一个脚本类,Android设备控制脚本类,继承 AndroidBotMain,实现具体的设备操作逻辑
class CustomAndroidScript(AndroidBotMain):
    """Android设备控制脚本"""
    Log_Level = "INFO"    # 日志级别 INFO不打印
    Log_Storage = False     # 是否存储日志
    resolution_a = (2400, 1080)  # 抓取过坐标的设备标准分辨率
    _instances = {}  # 类变量保存所有实例 {device_id: instance}
    _thread_ref = None  # 保存线程引用
    
    # 4. Hid底层是windows来操作的所以需要windows驱动
    WinDriving = WinBotMain._build("127.0.0.1", 8889, True)
    WinDriving.init_hid()
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.connection_info = dict()
        self.登录区服 = None  # 登录区服
        self.业务标记 = None  # 初始化业务标记属性
        
    @classmethod
    def set_thread_reference(cls, thread_ref):
        """设置线程引用"""
        cls._thread_ref = thread_ref   
                 
    def Android_init(self):
        """获取完整的设备连接信息（模式+详细信息）"""
        # 检查设备操作模式
        if self.click((0, 0)):  # 通过无障碍点击坐标判断Model
            print("设备模式检测结果: 无障碍模式")
            mode = "无障碍"
        else:
            print("无障碍模式不可用，尝试初始化HID模式...")
            # 初始化hid
            if self.init_hid(self.WinDriving):
                print("设备模式检测结果: HID模式")
                mode = "HID"
                self.init_hid(self.WinDriving) #HID初始化
            else:
                print("无法获取操作模式异常，请检查设备是否开启无障碍或HID模式")
                self.close_driver()  # 关闭设备连接 关闭实例线程
                return None
        
        # 设备模式正常，获取完整信息
        device_info = {
            'device_ip': self.get_device_ip(),      # 获取设备 IP
            'android_id': self.get_android_id(),    # 获取设备 ID
            'group_id': self.get_group_id(),        # 获取设备投屏 组号
            'identifier': self.get_identifier(),    # 获取设备投屏 编号
            'title': self.get_title(),              # 获取设备投屏 标题
            'resolution_b': self.get_window_size(),  # 获取设备 窗口大小
            'mode': mode
        }
                      
        print(f"设备信息获取完成，当前操作模式: {mode}")
        #初始化设备OCR 服务
        title = device_info['title']
        if title == "真机":
            print("真机,初始化设备OCR 服务")
            result = self.init_ocr_server("127.0.0.1", False, False, False) #使用手机内置ocr
        elif title == "云机":
            print("云机,初始化设备OCR 服务")
            result = self.init_ocr_server("*************", False, True, True) #服务器地址ocr
            print(result)
        else:
            print("设备标题不是真机也不是云机,无法初始化OCR服务")
            self.close_driver()  # 关闭设备连接 关闭实例线程
            return None
        self.set_android_timeout(60) #设置安卓客户端接收超时,设备连接后60秒没有执行任何函数断开连接关闭实例线程
        
        # 将设备信息保存到实例变量
        self.connection_info = device_info
        
        # 发送设备信息到UI界面
        from UI.UI_func import send_device_info_to_ui
        send_device_info_to_ui(
            self.connection_info, 
            self._thread_ref, 
            getattr(self, '登录区服', '') or ""
        )
        
        return device_info
          
    def script_main(self):
        device_info = self.Android_init() # 获取设备信息      
        # 主函数死循环时手机app连接断开异常捕获跳出死循环,关闭实例线程,释放资源
        while True:
            try:
                # 死循环中必须加入aibote函数代码
                # get = self.get_installed_packages() #获取已安装app的包名(不包含系统APP)
                # print(get)
                result = self.get_text((0,0,0,0), (0,0,0), 1)
                print(result)
                print("我是个死循环")
                time.sleep(2)
            # 服务端捕获客户端断开异常跳出执行线程循环结束连接
            except OSError as e:
                print(f"捕获到手机app连接断开异常:{e},结束线程")
                # 通过IP发送删除设备信号
                if self.connection_info and self._thread_ref:
                    device_ip = self.connection_info.get('device_ip', '')
                    if device_ip:
                        self._thread_ref.device_removed_by_ip.emit(device_ip)
                        print(f"已发送删除设备信号，IP: {device_ip}")
                break
            # 捕获其他非连接断开异常
            except Exception as e:
                print(f"捕获到其他异常:{e}")
                # 对于其他严重异常也删除设备记录
                if self.connection_info and self._thread_ref:
                    device_ip = self.connection_info.get('device_ip', '')
                    if device_ip:
                        self._thread_ref.device_removed_by_ip.emit(device_ip)
                        print(f"异常处理：已发送删除设备信号，IP: {device_ip}")
                print(e)
                break

# 程序入口
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # 使用更兼容的方式注册元类型
    try:
        # 通过信号连接自动注册类型
        app.aboutToQuit.connect(lambda: None)  # 触发元类型系统初始化
    except Exception as e:
        print(f"初始化元类型系统时出错: {str(e)}")
    
    # 高DPI设置(已在文件开头设置)
    QApplication.setHighDpiScaleFactorRoundingPolicy(
        Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
    )
    
    try:
        from UI import CyberpunkMainWindow
        window = CyberpunkMainWindow()
        window.show()
        sys.exit(app.exec_())
    except Exception as e:
        print(f"Error: {str(e)}")
        print(traceback.format_exc())