from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QTableWidget,
                            QHeaderView, QPushButton, QHBoxLayout)
from PyQt5.QtCore import Qt

class AccountManagementPage(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        # 初始化事件过滤器
        self._event_filter_installed = False
        
        print("账号管理页面已初始化（功能已禁用）")
            
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 账号表格
        self.table = QTableWidget()
        self.table.setContextMenuPolicy(Qt.NoContextMenu)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setColumnCount(10)
        self.table.setHorizontalHeaderLabels([
            "账号", "密码", "手机ID", "登录状态", "登录区服",
            "注册姓名", "手机", "身份证", "封号状态", "备注"
        ])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.table.verticalHeader().setVisible(False)
        
        # 表格样式
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: rgba(30, 35, 60, 0.5);
                border: 1px solid rgba(0, 245, 255, 0.2);
                color: #C0C0C0;
                font-family: "Segoe UI";
                font-size: 9pt;
                gridline-color: rgba(0, 245, 255, 0.1);
            }
            QTableWidget QTableCornerButton::section,
            QTableWidget::viewport {
                background-color: rgba(30, 35, 60, 0.5);
                border: none;
            }
            QHeaderView::section {
                background-color: transparent;
                color: #00F5FF;
                padding-left: 8px;
                padding-right: 8px;
                border: none;
                font-family: "Segoe UI";
                font-size: 10pt;
                font-weight: normal;
            }
            QTableWidget::item {
                padding: 5px;
                text-align: center;
                vertical-align: middle;
                selection-background-color: transparent;
                selection-color: inherit;
                outline: none;
                border: none;
                alignment: AlignCenter;
            }
            QTableWidget::item:selected {
                background-color: transparent;
                color: inherit;
                outline: none;
                border: none;
            }
            QTableWidget {
                outline: none;
            }
        """)
        
        # 操作按钮
        btn_layout = QHBoxLayout()
        btn_layout.setContentsMargins(5, 5, 5, 5)
        btn_layout.setSpacing(5)
        
        self.add_btn = QPushButton("添加")
        self.add_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(37, 117, 252, 0.3);
                color: #00F5FF;
                border: 1px solid #00F5FF;
                border-radius: 4px;
                padding: 5px 10px;
                min-width: 60px;
                font-weight: bold;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: rgba(0, 245, 255, 0.5);
                color: white;
                border: 1px solid white;
            }
        """)
        # 移除按钮点击事件绑定，只保留UI

        self.edit_btn = QPushButton("编辑")
        # self.edit_btn.clicked.connect(self.edit_account)  # 已禁用

        self.delete_btn = QPushButton("删除")
        # self.delete_btn.clicked.connect(self.delete_account)  # 已禁用

        self.export_btn = QPushButton("导出")
        # self.export_btn.clicked.connect(self.export_accounts)  # 已禁用

        self.import_btn = QPushButton("导入")
        # self.import_btn.clicked.connect(self.import_accounts)  # 已禁用

        self.template_btn = QPushButton("模板下载")
        # self.template_btn.clicked.connect(self.download_template)  # 已禁用

        # 设置按钮样式
        for btn in [self.edit_btn, self.delete_btn, self.export_btn, 
                   self.import_btn, self.template_btn]:
            btn.setStyleSheet(self.add_btn.styleSheet())
        
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.edit_btn)
        btn_layout.addWidget(self.delete_btn)
        btn_layout.addWidget(self.export_btn)
        btn_layout.addWidget(self.import_btn)
        btn_layout.addWidget(self.template_btn)
        
        # 主内容布局
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        content_layout.addWidget(self.table)
        
        main_layout.addLayout(content_layout)
        main_layout.addLayout(btn_layout)
        
        # 连接表格双击信号已禁用
        # self.table.itemDoubleClicked.connect(lambda item:
        #     self.on_table_double_clicked(item) if item.column() == 0 else None)

    def load_data(self):
        """加载数据功能已禁用，显示空表格"""
        self.table.setRowCount(0)
        print("账号管理数据加载已禁用")

    def update_login_status(self, account, status):
        """更新登录状态功能已禁用"""
        print(f"更新登录状态功能已禁用: {account} -> {status}")
        return False

    def get_account_by_device_id(self, device_id):
        """通过设备ID获取账号功能已禁用"""
        print(f"通过设备ID获取账号功能已禁用: {device_id}")
        return None

    def clear_selection(self):
        """清除选择"""
        self.table.clearSelection()
