import time
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QFrame, QSplitter, QStackedWidget, QApplication)
from .样式 import CyberpunkTheme
from .标题栏 import TitleBarComponent
from .导航面板 import NavigationComponent
from .服务控制 import ServiceControlComponent
from .输出窗口 import OutputWindowComponent


class CyberpunkMainWindow(QMainWindow):
    """主窗口类，负责UI界面和线程管理,继承自QMainWindow，提供完整的窗口功能"""
    
    def __init__(self):
        super().__init__()
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 主窗口尺寸
        self.setFixedSize(1000, 600)

        # 初始化所有UI组件
        self.init_ui_components()

    def init_ui_components(self):
        """初始化所有UI组件"""
        # 创建主容器和主布局
        self.main_container = QWidget()
        self.main_container.setObjectName("MainContainer")
        self.setCentralWidget(self.main_container)
        
        self.main_layout = QVBoxLayout(self.main_container)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 创建标题栏和服务控制区域
        self.create_header()
        
        # 创建主内容区域
        self.create_main_content()
        
        # 初始化后台线程
        self.init_thread()

        # 表格更新防抖机制
        self.pending_device_updates = {}
        
        # 设置基础样式
        self.setup_styles()

    def init_thread(self):
        """初始化后台线程 - 从main模块导入AndroidControlThread"""
        try:
            # 从main模块导入AndroidControlThread
            import __main__
            if hasattr(__main__, 'AndroidControlThread'):
                AndroidControlThread = __main__.AndroidControlThread
            else:
                # 如果__main__没有，尝试从main.py导入
                import main
                AndroidControlThread = main.AndroidControlThread
                
            self.thread = AndroidControlThread()
            self.thread.progress.connect(self.update_ui)
            self.thread.device_info_updated.connect(self.update_device_table)
            self.thread.device_removed_by_ip.connect(self.remove_device_by_ip)
            # 连接服务控制信号
            if hasattr(self, 'service_control'):
                self.service_control.startService.connect(self.thread.run)
                self.service_control.stopService.connect(self.thread.stop_service)
        except (ImportError, AttributeError) as e:
            print(f"无法导入AndroidControlThread: {e}")

    def setup_styles(self):
        """设置赛博朋克基础样式（优化性能）"""
        # 应用UI性能优化
        from UI.UI_func import optimize_ui_performance
        optimize_ui_performance()
        
        # 应用样式
        self.main_container.setStyleSheet(CyberpunkTheme.MAIN_CONTAINER_STYLE)
        QApplication.setFont(CyberpunkTheme.BODY_FONT)
    
    def create_header(self):
        """创建标题栏和服务控制区域"""
        header_widget = QWidget()
        header_widget.setFixedHeight(38)
        header_widget.setStyleSheet("background-color: transparent;")
        
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(0)
        
        # 标题栏组件
        self.title_bar = TitleBarComponent(self)
        header_layout.addWidget(self.title_bar)
        
        # 服务控制组件
        self.service_control = ServiceControlComponent(self)
        header_layout.addWidget(self.service_control)
        
        # 窗口控制按钮
        self._create_window_controls(header_layout)
        
        self.main_layout.addWidget(header_widget)
    
    def _create_window_controls(self, layout):
        """创建窗口控制按钮"""
        from PyQt5.QtWidgets import QPushButton
        from .样式 import CyberpunkTheme
        
        btn_minimize = QPushButton("—")
        btn_minimize.setFixedSize(30, 30)
        btn_minimize.setStyleSheet(CyberpunkTheme.CONTROL_BUTTON_STYLE)
        btn_minimize.clicked.connect(self.showMinimized)
        
        btn_close = QPushButton("×")
        btn_close.setFixedSize(30, 30)
        btn_close.setStyleSheet(CyberpunkTheme.CONTROL_BUTTON_STYLE)
        btn_close.clicked.connect(self.close)
        
        layout.addWidget(btn_minimize)
        layout.addWidget(btn_close)
    
    def create_main_content(self):
        """创建主内容区域"""
        content_frame = QFrame()
        content_frame.setFrameShape(QFrame.NoFrame)
        content_frame.setStyleSheet("background-color: transparent;")
        
        self.content_layout = QHBoxLayout(content_frame)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(5)
        
        # 左侧导航面板
        self.navigation = NavigationComponent(self)
        self.content_layout.addWidget(self.navigation)
        
        # 右侧内容区
        self.create_content_panel()
        
        self.main_layout.addWidget(content_frame)
    
    def create_content_panel(self):
        """创建右侧内容面板"""
        splitter = QSplitter(Qt.Vertical)
        splitter.setStyleSheet(CyberpunkTheme.SPLITTER_STYLE)
        
        # 上方主区域
        main_widget = QWidget()
        main_widget.setMinimumHeight(200)
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加主区域
        self.create_card_area(main_layout)
        
        # 输出窗口组件
        self.output_window = OutputWindowComponent()
        self.output_window.setMinimumHeight(100)
        
        splitter.addWidget(main_widget)
        splitter.addWidget(self.output_window)
        splitter.setSizes([600, 200])
        splitter.setCollapsible(0, False)
        splitter.setCollapsible(1, False)
        
        self.content_layout.addWidget(splitter)
    
    def create_card_area(self, layout):
        """创建卡片区域"""
        content_widget = QWidget()
        content_widget.setStyleSheet(CyberpunkTheme.CONTENT_WIDGET_STYLE)
        
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        
        # 创建堆叠窗口
        self.stacked_widget = QStackedWidget()
        self.stacked_widget.setStyleSheet("background-color: transparent;")
        
        # 创建连接管理页面(设备表格)
        self.create_phone_table_page()
        
        # 创建账号管理页面
        from UI.账号管理 import AccountManagementPage
        self.account_page = AccountManagementPage()
        self.stacked_widget.addWidget(self.account_page)

        # 创建系统设置页面
        self.create_settings_page()
        
        content_layout.addWidget(self.stacked_widget)
        layout.addWidget(content_widget)

    def create_phone_table_page(self):
        """创建连接管理页面(设备表格)"""
        from UI.连接管理 import ConnectionManagementPage
        self.connection_page = ConnectionManagementPage(self)
        self.stacked_widget.addWidget(self.connection_page)
    
    def create_settings_page(self):
        """创建系统设置页面"""
        from UI.系统设置 import SystemSettingsPage
        self.settings_page = SystemSettingsPage(self)
        self.stacked_widget.addWidget(self.settings_page)
    
    def append_output(self, typ, msg):
        """向输出窗口添加消息"""
        self.output_window.append_output(typ, msg)
    
    def update_ui(self, message):
        """
        更新UI的槽函数（优化性能，减少阻塞）
        :param message: 接收到的消息文本
        """
        # 直接在主线程中执行，因为PyQt信号槽已经确保线程安全
        self.append_output("信息", message)
        
        # 根据消息内容更新UI状态（简化逻辑，避免复杂操作）
        if "服务启动" in message or "服务关闭" in message:
            # 更新服务控制组件状态
            if hasattr(self, 'service_control'):
                self.service_control.update_service_status(message)
        
    def _update_ui_safe(self):
        """内部安全更新方法（保留备用）""" 
        pass
    
    def update_device_table(self, device_info):
        """更新设备表格(优化性能，线程安全)"""
        try:
            from UI.UI_func import batch_update_ui_with_debounce
            
            # 使用防抖机制批量更新，避免频繁刷新UI
            def _update_table(data):
                device_id = data.get("device_id", "")
                if hasattr(self, 'connection_page'):
                    self.connection_page.update_device_table(data)
                    self.append_output("信息", f"设备信息已更新: {device_id}")
                else:
                    self.append_output("错误", "连接管理页面未初始化")
            
            # 使用防抖更新，减少UI刷新频率
            batch_update_ui_with_debounce(_update_table, device_info, 500)
            
        except Exception as e:
            self.append_output("错误", f"更新设备表格失败: {str(e)}")

    def remove_device_by_ip(self, device_ip):
        """通过IP删除设备记录"""
        try:
            if hasattr(self, 'connection_page'):
                self.connection_page.remove_device_by_ip(device_ip)
            else:
                self.append_output("错误", "连接管理页面未初始化")
        except Exception as e:
            self.append_output("错误", f"删除设备记录失败: {str(e)}")

    def center_dialog(self, dialog):
        """将对话框居中显示在主窗口"""
        dialog_rect = dialog.rect()
        main_rect = self.geometry()
        
        # 计算居中位置
        x = main_rect.x() + (main_rect.width() - dialog_rect.width()) // 2
        y = main_rect.y() + (main_rect.height() - dialog_rect.height()) // 2
        
        # 确保对话框不会超出屏幕
        screen_rect = QApplication.desktop().availableGeometry()
        x = max(0, min(x, screen_rect.width() - dialog_rect.width()))
        y = max(0, min(y, screen_rect.height() - dialog_rect.height()))
        
        dialog.move(x, y)