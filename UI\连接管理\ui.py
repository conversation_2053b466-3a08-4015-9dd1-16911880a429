from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QColor
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, 
                            QHeaderView, QSizePolicy)
import time

class ConnectionManagementPage(QWidget):
    update_requested = pyqtSignal(dict)  # 设备更新信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.pending_device_updates = {}
        self.init_ui()

    def init_ui(self):
        """初始化连接管理UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建手机连接状态表格
        self.phone_table = QTableWidget()
        self.phone_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.phone_table.setColumnCount(12)
        self.phone_table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.phone_table.setHorizontalHeaderLabels([
            "手机ID", "手机IP", "分组", "编号", 
            "登录账号", "区服", "当前地图", 
            "角色名", "等级", "当前动作", "操作模式", "连接状态"
        ])
        
        # 设置表格样式(与账号管理页面一致)
        self.phone_table.setStyleSheet("""
            QTableWidget {
                background-color: rgba(30, 35, 60, 0.5);
                border: 1px solid rgba(0, 245, 255, 0.2);
                color: #C0C0C0;
                font-family: "Segoe UI";
                font-size: 9pt;
                gridline-color: rgba(0, 245, 255, 0.1);
            }
            QTableWidget QTableCornerButton::section,
            QTableWidget::viewport {
                background-color: rgba(30, 35, 60, 0.5);
                border: none;
            }
            QHeaderView::section {
                background-color: transparent;
                color: #00F5FF;
                padding-left: 8px;
                padding-right: 8px;
                border: none;
                font-family: "Segoe UI";
                font-size: 10pt;
                font-weight: normal;
            }
            QTableWidget::item {
                padding: 5px;
                text-align: center;
                vertical-align: middle;
                selection-background-color: transparent;
                selection-color: inherit;
                outline: none;
                border: none;
            }
            QTableWidget::item:selected {
                background-color: transparent;
                color: inherit;
                outline: none;
                border: none;
            }
            QTableWidget::item:selected:!active {
                background-color: transparent;
                color: inherit;
            }
            QTableWidget {
                outline: none;
            }
        """)
        
        # 设置表格属性
        self.phone_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.phone_table.verticalHeader().setVisible(False)
        self.phone_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.phone_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.phone_table.setAlternatingRowColors(False)
        self.phone_table.setSelectionMode(QTableWidget.MultiSelection)
        
        # 初始空表格
        self.phone_table.setRowCount(0)
        
        # 添加表格到布局
        layout.addWidget(self.phone_table)
        
        # 确保滚动条对齐
        self.phone_table.setStyleSheet(self.phone_table.styleSheet() + """
            QScrollBar:vertical {
                width: 12px;
                margin: 0px;
                padding: 0px;
            }
            QScrollBar::add-line:vertical, 
            QScrollBar::sub-line:vertical,
            QScrollBar::add-page:vertical, 
            QScrollBar::sub-page:vertical {
                border: none;
                margin: 0px;
                padding: 0px;
            }
        """)

    def update_device_table(self, device_info):
        """更新设备表格(带防抖机制)"""
        try:
            device_id = device_info["device_id"]
            self.pending_device_updates[device_id] = device_info
            self._perform_table_update()
        except Exception as e:
            if self.main_window:
                self.main_window.append_output("错误", f"更新设备表格失败: {str(e)}")

    def _perform_table_update(self):
        """执行实际的表格更新"""
        try:
            for device_id, device_info in self.pending_device_updates.items():
                row_count = self.phone_table.rowCount()
                found = False
                for row in range(row_count):
                    if self.phone_table.item(row, 0).text() == device_id:
                        self._update_device_row(row, device_info)
                        found = True
                        break
                
                if not found:
                    row = self.phone_table.rowCount()
                    self.phone_table.insertRow(row)
                    self._update_device_row(row, device_info)
            
            self.pending_device_updates.clear()
        except Exception as e:
            if self.main_window:
                self.main_window.append_output("错误", f"执行表格更新失败: {str(e)}")

    def remove_device_by_ip(self, device_ip):
        """通过IP删除设备记录"""
        try:
            rows_to_remove = []
            # 查找匹配IP的行
            for row in range(self.phone_table.rowCount()):
                ip_item = self.phone_table.item(row, 1)  # 第1列是IP
                if ip_item and ip_item.text() == device_ip:
                    rows_to_remove.append(row)
            
            # 从后向前删除行，避免索引变化
            for row in reversed(rows_to_remove):
                device_id_item = self.phone_table.item(row, 0)
                device_id = device_id_item.text() if device_id_item else "未知"
                self.phone_table.removeRow(row)
                if self.main_window:
                    self.main_window.append_output("信息", f"设备已断开连接并从表格中移除: IP={device_ip}, ID={device_id}")
                    
            if not rows_to_remove:
                if self.main_window:
                    self.main_window.append_output("警告", f"未找到IP为 {device_ip} 的设备记录")
                    
        except Exception as e:
            if self.main_window:
                self.main_window.append_output("错误", f"通过IP删除设备记录失败: {str(e)}")

    def clear_selection(self):
        """清除表格的所有选中状态"""
        self.phone_table.clearSelection()

    def _update_device_row(self, row, device_info):
        """更新设备表格的某一行"""
        def create_centered_item(text):
            item = QTableWidgetItem(str(text))
            item.setTextAlignment(Qt.AlignCenter)
            return item
            
        # 根据传入的device_info更新对应字段
        self.phone_table.setItem(row, 0, create_centered_item(device_info.get("device_id", "")))
        self.phone_table.setItem(row, 1, create_centered_item(device_info.get("ip", "")))
        self.phone_table.setItem(row, 2, create_centered_item(device_info.get("group", "")))
        self.phone_table.setItem(row, 3, create_centered_item(device_info.get("identifier", "")))
        self.phone_table.setItem(row, 4, create_centered_item(device_info.get("account", "")))
        self.phone_table.setItem(row, 5, create_centered_item(device_info.get("server", "")))
        self.phone_table.setItem(row, 6, create_centered_item(device_info.get("map", "")))
        self.phone_table.setItem(row, 7, create_centered_item(device_info.get("character", "")))
        self.phone_table.setItem(row, 8, create_centered_item(device_info.get("level", "")))
        self.phone_table.setItem(row, 9, create_centered_item(device_info.get("action", "")))
        self.phone_table.setItem(row, 10, create_centered_item(device_info.get("mode", "")))

        # 连接状态用不同颜色显示
        status = device_info.get("status", "未知")
        status_item = QTableWidgetItem(status)
        status_item.setTextAlignment(Qt.AlignCenter)
        
        if status == "已连接":
            status_color = QColor("#00FF00")  # 绿色
        elif status == "断开连接":
            status_color = QColor("#FF5555")  # 红色
        else:
            status_color = QColor("#FFA500")  # 橙色
            
        status_item.setForeground(status_color)
        self.phone_table.setItem(row, 11, status_item)
