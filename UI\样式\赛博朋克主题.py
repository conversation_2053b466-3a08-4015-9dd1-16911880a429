from PyQt5.QtGui import QFont


class CyberpunkTheme:
    """赛博朋克主题样式管理类"""
    
    # 主容器样式
    MAIN_CONTAINER_STYLE = """
        #MainContainer {
            background-color: rgba(20, 25, 45, 0.7);
            border-radius: 12px;
            border: 1px solid rgba(0, 245, 255, 0.3);
        }
    """
    
    # 四级字体系统
    TITLE_FONT = QFont("Segoe UI Semibold", 12)
    SUBTITLE_FONT = QFont("Segoe UI", 10) 
    BODY_FONT = QFont("Segoe UI", 9)
    CODE_FONT = QFont("Consolas", 9)
    
    # 表格样式
    PHONE_TABLE_STYLE = """
        QTableWidget {
            background-color: rgba(30, 35, 60, 0.5);
            border: 1px solid rgba(0, 245, 255, 0.2);
            color: #C0C0C0;
            font-family: "Segoe UI";
            font-size: 9pt;
            gridline-color: rgba(0, 245, 255, 0.1);
        }
        QTableWidget QTableCornerButton::section,
        QTableWidget::viewport {
            background-color: rgba(30, 35, 60, 0.5);
            border: none;
        }
        QHeaderView::section {
            background-color: rgba(106, 17, 203, 0.3);
            color: #00F5FF;
            padding-left: 8px;
            padding-right: 8px;
            border: none;
            font-family: "Segoe UI";
            font-size: 10pt;
            font-weight: normal;
        }
        QTableWidget::item {
            padding: 5px;
            text-align: center;
            vertical-align: middle;
        }
        QTableWidget::item:selected {
            background-color: rgba(37, 117, 252, 0.5);
            color: #C0C0C0;
        }
    """
    
    # 导航按钮样式
    NAV_BUTTON_STYLE = """
        QPushButton {
            color: #C0C0C0;
            background-color: transparent;
            text-align: left;
            padding-left: 10px;
            border-radius: 4px;
            font-size: 12px;
        }
        QPushButton:hover {
            color: #00F5FF;
            background-color: rgba(106, 17, 203, 0.3);
        }
        QPushButton:pressed {
            background-color: rgba(37, 117, 252, 0.5);
        }
        QPushButton:checked {
            color: #00F5FF;
            background-color: rgba(37, 117, 252, 0.5);
            border-left: 3px solid #00F5FF;
        }
    """
    
    # 标题标签样式
    TITLE_LABEL_STYLE = """
        QLabel {
            color: #00F5FF;
            font-family: "Segoe UI Semibold";
            font-size: 14pt;
            font-weight: 600;
            letter-spacing: 1px;
            padding-bottom: 2px;
        }
    """
    
    # 服务状态标签样式
    STATUS_LABEL_STYLE = """
        QLabel {
            color: #FF5555;
            font-family: "Segoe UI";
            font-size: 11pt;
            font-weight: 600;
            letter-spacing: 1px;
            min-width: 80px;
            text-align: center;
            text-transform: uppercase;
        }
    """
    
    # 启动按钮样式
    START_BUTTON_STYLE = """
        QPushButton {
            color: #00FF00;
            background-color: rgba(37, 117, 252, 0.3);
            border: 1px solid #00FF00;
            border-radius: 4px;
            padding: 8px 12px;
            margin: 0px;
            font-family: "Segoe UI";
            font-size: 9pt;
            min-width: 100px;
        }
        QPushButton:hover {
            background-color: rgba(0, 255, 0, 0.5);
        }
    """
    
    # 停止按钮样式
    STOP_BUTTON_STYLE = """
        QPushButton {
            color: #FF5555;
            background-color: rgba(37, 117, 252, 0.3);
            border: 1px solid #FF5555;
            border-radius: 4px;
            padding: 8px 12px;
            margin: 0px;
            font-family: "Segoe UI";
            font-size: 9pt;
            min-width: 100px;
        }
        QPushButton:hover {
            background-color: rgba(255, 85, 85, 0.5);
        }
    """
    
    # 窗口控制按钮样式
    CONTROL_BUTTON_STYLE = """
        QPushButton {
            color: #00F5FF;
            background-color: rgba(37, 117, 252, 0.3);
            border-radius: 15px;
            border: 1px solid rgba(0, 245, 255, 0.3);
            font-size: 16px;
        }
        QPushButton:hover {
            background-color: rgba(37, 117, 252, 0.5);
        }
        QPushButton:pressed {
            background-color: rgba(0, 245, 255, 0.7);
        }
    """
    
    # 输出文本框样式
    OUTPUT_TEXT_STYLE = """
        QTextEdit {
            background-color: rgba(20, 25, 45, 0.7);
            color: #E0E0E0;
            border: none;
            font-family: Consolas;
            font-size: 9pt;
            line-height: 1.4;
            padding: 10px;
        }
    """
    
    # 清空按钮样式
    CLEAR_BUTTON_STYLE = """
        QPushButton {
            color: #FF5555;
            background-color: rgba(106, 17, 203, 0.3);
            border: 1px solid #FF5555;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 11px;
        }
        QPushButton:hover {
            background-color: rgba(255, 85, 85, 0.5);
        }
    """
    
    # 导航面板样式
    NAV_FRAME_STYLE = """
        background-color: rgba(20, 25, 45, 0.5);
        border-radius: 8px;
        border: 1px solid rgba(0, 245, 255, 0.2);
    """
    
    # 内容区域样式
    CONTENT_WIDGET_STYLE = """
        background-color: rgba(20, 25, 45, 0.5);
        border-radius: 8px;
        border: 1px solid rgba(0, 245, 255, 0.2);
    """
    
    # 输出框架样式
    OUTPUT_FRAME_STYLE = """
        background-color: rgba(30, 35, 60, 0.5);
        border-radius: 8px;
        border: 1px solid rgba(0, 245, 255, 0.2);
    """
    
    # 分割器样式
    SPLITTER_STYLE = """
        QSplitter::handle {
            background-color: rgba(0, 245, 255, 0.1);
            height: 2px;
        }
    """
    
    @classmethod
    def get_service_status_style(cls, status_type):
        """根据状态类型返回对应样式"""
        if status_type == "running":
            return """
                QLabel {
                    color: #00FF00;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 80px;
                    text-align: center;
                }
            """
        elif status_type == "loading":
            return """
                QLabel {
                    color: #FFA500;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 80px;
                    text-align: center;
                }
            """
        else:  # stopped
            return """
                QLabel {
                    color: #FF5555;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 80px;
                    text-align: center;
                }
            """
    
    @classmethod
    def get_button_disabled_style(cls, button_type):
        """获取按钮禁用样式"""
        if button_type == "start":
            return """
                QPushButton {
                    color: #606060;
                    background-color: rgba(37, 117, 252, 0.1);
                    border: 1px solid #606060;
                    border-radius: 4px;
                    padding: 6px 15px;
                    font-size: 12px;
                    min-width: 100px;
                }
            """
        else:  # stop
            return """
                QPushButton {
                    color: #606060;
                    background-color: rgba(106, 17, 203, 0.1);
                    border: 1px solid #606060;
                    border-radius: 4px;
                    padding: 6px 15px;
                    font-size: 12px;
                    min-width: 100px;
                }
            """