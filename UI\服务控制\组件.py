from PyQt5.QtCore import pyqtSignal
from PyQt5.QtWidgets import QWidget, QHBoxLayout, QLabel, QPushButton, QMessageBox
from ..样式 import CyberpunkTheme


class ServiceControlComponent(QWidget):
    """服务控制组件"""
    
    # 定义信号
    startService = pyqtSignal(str, int)  # ip, port
    stopService = pyqtSignal()
    
    def __init__(self, main_window, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self.init_ui()
    
    def init_ui(self):
        """初始化服务控制UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 服务状态显示
        self.status_label = QLabel("服务未启动")
        self.status_label.setStyleSheet(CyberpunkTheme.STATUS_LABEL_STYLE)
        
        # 创建服务控制按钮
        self._create_service_buttons(layout)
    
    def _create_service_buttons(self, layout):
        """创建服务控制按钮"""
        self.start_btn = QPushButton("▶ 启动服务")
        self.stop_btn = QPushButton("■ 关闭服务")
        self.stop_btn.setEnabled(False)
        
        # 设置按钮样式
        self.start_btn.setStyleSheet(CyberpunkTheme.START_BUTTON_STYLE)
        self.stop_btn.setStyleSheet(CyberpunkTheme.STOP_BUTTON_STYLE)
        
        # 连接信号
        self.start_btn.clicked.connect(self.start_service)
        self.stop_btn.clicked.connect(self.stop_service)
        
        # 添加组件到布局：状态标签在左边，然后是按钮
        layout.addWidget(self.status_label)
        layout.addWidget(self.start_btn)
        layout.addWidget(self.stop_btn)
    
    def start_service(self):
        """启动服务"""
        if not self.start_btn.isEnabled():
            return
            
        try:
            if hasattr(self.main_window, 'settings_page'):
                ip, port = self.main_window.settings_page.get_server_info()
            else:
                ip, port = "127.0.0.1", 9999  # 默认值
            
            # 禁用按钮防止重复点击
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)
            
            # 设置加载状态样式
            self.start_btn.setStyleSheet(CyberpunkTheme.get_button_disabled_style("start"))
            self.status_label.setText("启动中...")
            self.status_label.setStyleSheet(CyberpunkTheme.get_service_status_style("loading"))
            
            if hasattr(self.main_window, 'append_output'):
                self.main_window.append_output("系统", f"正在启动服务 {ip}:{port}...")
            
            # 更新线程配置并发射启动信号
            if hasattr(self.main_window, 'thread'):
                self.main_window.thread.set_server_info(ip, port)
                self.startService.emit(ip, port)
            
        except Exception as e:
            if hasattr(self.main_window, 'append_output'):
                self.main_window.append_output("错误", f"启动服务失败: {str(e)}")
            self._reset_buttons()
    
    def stop_service(self):
        """停止服务"""
        if not self.stop_btn.isEnabled():
            return
            
        try:
            # 创建赛博朋克风格确认对话框
            msg_box = QMessageBox()
            msg_box.setWindowTitle("确认停止服务")
            msg_box.setText("确定要停止服务吗？")
            msg_box.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
            msg_box.setDefaultButton(QMessageBox.Cancel)
            
            # 设置赛博朋克样式
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: rgba(20, 25, 45, 0.95);
                    border: 2px solid #00F5FF;
                    border-top: 3px solid qlineargradient(
                        x1:0, y1:0, x2:0, y2:1,
                        stop:0 #00F5FF, stop:1 transparent
                    );
                }
                QLabel {
                    color: #00F5FF;
                    font-size: 14px;
                    margin-top: 5px;
                }
                QPushButton {
                    color: #00F5FF;
                    background-color: rgba(106, 17, 203, 0.3);
                    border: 1px solid #00F5FF;
                    border-radius: 4px;
                    padding: 5px 15px;
                    min-width: 80px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: rgba(0, 245, 255, 0.5);
                    color: white;
                    border: 1px solid white;
                }
            """)
            
            if msg_box.exec_() == QMessageBox.Ok:
                # 设置停止中状态
                self.stop_btn.setEnabled(False)
                self.stop_btn.setStyleSheet(CyberpunkTheme.get_button_disabled_style("stop"))
                self.status_label.setText("停止中...")
                self.status_label.setStyleSheet(CyberpunkTheme.get_service_status_style("loading"))
                
                # 发射停止信号
                self.stopService.emit()
                
        except Exception as e:
            if hasattr(self.main_window, 'append_output'):
                self.main_window.append_output("错误", f"停止服务时发生错误: {str(e)}")
            self._reset_buttons()
    
    def _reset_buttons(self):
        """重置按钮状态"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
        # 启动按钮样式
        self.start_btn.setStyleSheet(CyberpunkTheme.START_BUTTON_STYLE)
        
        # 停止按钮禁用状态
        self.stop_btn.setStyleSheet(CyberpunkTheme.get_button_disabled_style("stop"))
        
        # 状态标签样式
        self.status_label.setText("服务未启动")
        self.status_label.setStyleSheet(CyberpunkTheme.get_service_status_style("stopped"))
    
    def update_service_status(self, message):
        """更新服务状态"""
        if "服务启动" in message:
            # 启动成功后启用停止按钮
            self.stop_btn.setEnabled(True)
            self.stop_btn.setStyleSheet(CyberpunkTheme.STOP_BUTTON_STYLE)
            
            if hasattr(self.main_window, 'thread'):
                self.status_label.setText(f"服务已启动 {self.main_window.thread.ip}:{self.main_window.thread.port}")
            else:
                self.status_label.setText("服务已启动")
            self.status_label.setStyleSheet(CyberpunkTheme.get_service_status_style("running"))
            
        elif "服务关闭" in message:
            # 停止服务后重置按钮状态
            self._reset_buttons()