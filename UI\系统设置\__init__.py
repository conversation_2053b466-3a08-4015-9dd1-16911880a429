from PyQt5.QtWidgets import QVBoxLayout, QWidget
from .服务器配置 import ServerConfigTab

class SystemSettingsPage(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.init_ui()

    def init_ui(self):
        """初始化系统设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 直接添加服务器配置组件
        self.server_tab = ServerConfigTab(self.main_window)
        layout.addWidget(self.server_tab)

    def get_server_info(self):
        """获取服务器配置信息"""
        return self.server_tab.get_server_info()

__all__ = ['SystemSettingsPage']
